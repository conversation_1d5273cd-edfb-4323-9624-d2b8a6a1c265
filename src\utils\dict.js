/**
 * 字典管理模块
 * 提供统一的字典数据管理功能
 */

// 用户状态字典
export const USER_STATUS = {
  ACTIVE: { value: 1, label: '正常', color: '#67C23A' },
  INACTIVE: { value: 0, label: '禁用', color: '#F56C6C' },
  PENDING: { value: 2, label: '待审核', color: '#E6A23C' }
}

// 订单状态字典
export const ORDER_STATUS = {
  IN_PROGRESS: { value: 1, label: '进行中', color: '#409EFF' },
  PENDING: { value: 0, label: '待开始', color: '#E6A23C' },
  COMPLETED: { value: 2, label: '已完成', color: '#67C23A' }
}



// 性别字典
export const GENDER = {
  MALE: { value: 1, label: '男' },
  FEMALE: { value: 2, label: '女' },
  UNKNOWN: { value: 0, label: '未知' }
}


// 字典工具类
class DictManager {
  /**
   * 根据值获取字典项
   * @param {Object} dict - 字典对象
   * @param {*} value - 要查找的值
   * @returns {Object|null} 字典项或null
   */
  static getByValue(dict, value) {
    for (const key in dict) {
      if (dict[key].value === value) {
        return dict[key]
      }
    }
    return null
  }

  /**
   * 根据值获取标签
   * @param {Object} dict - 字典对象
   * @param {*} value - 要查找的值
   * @param {string} defaultLabel - 默认标签
   * @returns {string} 标签
   */
  static getLabel(dict, value, defaultLabel = '未知') {
    const item = this.getByValue(dict, value)
    return item ? item.label : defaultLabel
  }

  /**
   * 根据值获取颜色
   * @param {Object} dict - 字典对象
   * @param {*} value - 要查找的值
   * @param {string} defaultColor - 默认颜色
   * @returns {string} 颜色值
   */
  static getColor(dict, value, defaultColor = '#909399') {
    const item = this.getByValue(dict, value)
    return item && item.color ? item.color : defaultColor
  }

  /**
   * 将字典转换为数组格式
   * @param {Object} dict - 字典对象
   * @returns {Array} 字典数组
   */
  static toArray(dict) {
    return Object.keys(dict).map(key => ({
      key,
      ...dict[key]
    }))
  }

  /**
   * 将字典转换为选项数组（用于下拉框等）
   * @param {Object} dict - 字典对象
   * @returns {Array} 选项数组
   */
  static toOptions(dict) {
    return Object.keys(dict).map(key => ({
      value: dict[key].value,
      label: dict[key].label,
      ...dict[key]
    }))
  }

  /**
   * 根据code获取字典项
   * @param {Object} dict - 字典对象
   * @param {*} code - 要查找的code
   * @returns {Object|null} 字典项或null
   */
  static getByCode(dict, code) {
    for (const key in dict) {
      if (dict[key].code === code) {
        return dict[key]
      }
    }
    return null
  }

  /**
   * 根据code获取标签
   * @param {Object} dict - 字典对象
   * @param {*} code - 要查找的code
   * @param {string} defaultLabel - 默认标签
   * @returns {string} 标签
   */
  static getLabelByCode(dict, code, defaultLabel = '未知') {
    const item = this.getByCode(dict, code)
    return item ? item.label : defaultLabel
  }
}

// 导出字典管理器
export { DictManager }

// 导出默认实例
export default DictManager
