 import dayjs from "dayjs"
import AuthApi from '@/apis/public/auth-api';
class OssClient {

  private _dir: string

  prefixDir = 'im/'

  static bucket: string = 'inquiry-ol';
  static region: string = 'oss-cn-beijing';
  static url: string = `https://${OssClient.bucket}.${OssClient.region}.aliyuncs.com`

  static iconUrl = OssClient.url + '/static/img/'


  constructor(type: OSS_FILE_TYPE = OSS_FILE_TYPE.AUDIO) {
    this.dir = type
  }

  set dir(type: string) {
    this._dir = type
  }
  get dir() {
    return this.prefixDir + this._dir + '/' + dayjs().format('YYYYMMDDH') + '/'
  }

  randFileName(fileType: string) {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15) + '.' + fileType
  }

  async upload(fileName: string, filePath: string) {
    const dir = this.dir + fileName

    const data = await AuthApi.ossAuthenticateSTS();
    console.log(data)

    const ossData = {
      key: dir,  //上传文件名称
      policy: data.policy,   //表单域
      'x-oss-signature-version': data.x_oss_signature_version,    //指定签名的版本和算法
      'x-oss-credential': data.x_oss_credential,   //指明派生密钥的参数集
      'x-oss-date': data.x_oss_date,   //请求的时间
      'x-oss-signature': data.signature,   //签名认证描述信息
      'x-oss-security-token': data.security_token,  //安全令牌
      success_action_status: "200",  //上传成功后响应状态码
    }

    return new Promise((resolve, reject) => {
      uni.uploadFile({
        url: OssClient.url,  // OSS上传地址
        filePath,
        name: 'file',
        formData: ossData,
        success(res) {
          // console.log('上传成功:', res, `${OssClient.url}/${dir}`)
          resolve(`${OssClient.url}/${dir}`)
        },
        fail(err) {
          console.log('上传失败:', err)
          reject(err)
        }
      })
    })

  }
}

export default OssClient;
