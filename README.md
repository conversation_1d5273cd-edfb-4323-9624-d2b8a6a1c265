# 陪诊服务

## 框架
1. 采用uniapp+vue3
3. 网络请求：
    - 封装uni.request，创建请求拦截器
    - 请求头自动携带token
    - 响应拦截器处理token过期、自动续期等逻辑
4. Token管理：
    - 单token模式
    - 每次请求成功后自动延长token有效期
    - token失效后自动跳转登录页
5. 环境变量
    - 开发环境：development
    - 生产环境：production
    - 通过process.env.NODE_ENV区分环境
    - 不同环境使用不同的API接口地址
    - 环境变量配置在manifest.json中


## 文档
1. 原型 ：https://lanhuapp.com/web/#/item/project/product?tid=61d39382-5886-4166-a652-c9ca6eb27714&pid=332beacc-8b96-46dc-a493-63356a078383&image_id=6ddf2ab6-0a69-4402-bfd7-301206c2d728&docId=6ddf2ab6-0a69-4402-bfd7-301206c2d728&docType=axure&versionId=649ea658-244c-4062-8006-281e11f623f3&pageId=9941ea14a9d04f76929d124430a81c31&share_type=quickShare&parentId=acba501bbeef459d921135679019edd9
2. ui：https://lanhuapp.com/web/#/item/project/detailDetach?pid=aedb2ef8-5789-4006-b68d-157c802b00fc&image_id=9a479937-eaef-4489-9faa-54fbd57ab339&tid=61d39382-5886-4166-a652-c9ca6eb27714&project_id=aedb2ef8-5789-4006-b68d-157c802b00fc&fromEditor=true&type=image