import { defineConfig } from 'vite'
import uni from '@dcloudio/vite-plugin-uni'
import { fileURLToPath, URL } from 'node:url';
import uniRouter from "unplugin-uni-router/vite";
console.log("fileURLToPath(new URL('./src', import.meta.url))",fileURLToPath(new URL('./src', import.meta.url)));

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    uni(),
     uniRouter()
  ],
  resolve: {
    alias: {
       '@': fileURLToPath(new URL('./src', import.meta.url)),
    }
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: '@import "@/uni.scss";'
      }
    }
  }
})
