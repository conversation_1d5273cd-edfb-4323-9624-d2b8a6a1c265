:deep(.uni-list) {
  .uni-list--border-top {
    display: none;
  }
  .uni-list--border-bottom {
    display: none;
  }
}
:deep(.uni-list-item) {
  .uni-icon-wrapper {
    margin-top: 24rpx;
    padding-right: 48rpx;
    padding-left: 24rpx;
  }
  .uni-list-item__container.container--right {
    padding-right: 0rpx;
  }
  .uni-list-item__container {
    margin-top: 24rpx;
    padding: 24rpx 48rpx;
    .uni-list-item__content {
      .uni-list-item__content-title {
        font-weight: 600;
        font-size: 32rpx;
        display: flex;
        height: 100%;
        align-items: center;
      }
    }
    .uni-list-item__extra {
      .uni-list-item__extra-text {
        font-size: 32rpx;
        color: $uni-text-color;
      }
    }
  }
}
