import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useAppStore = defineStore('app', () => {
  // 应用版本信息
  const appVersion = ref('1.0.0')
  
  // 应用配置
  const appConfig = ref({
    name: '陪护服务',
    description: '陪护服务小程序',
    theme: 'default'
  })
  
  // 系统信息
  const systemInfo = ref({})
  
  // 网络状态
  const networkType = ref('unknown')
  const isOnline = ref(true)
  
  // 计算属性
  const appName = computed(() => appConfig.value.name)
  const appDescription = computed(() => appConfig.value.description)
  
  // 获取系统信息
  const getSystemInfo = () => {
    try {
      const info = uni.getSystemInfoSync()
      systemInfo.value = info
      return info
    } catch (error) {
      console.error('获取系统信息失败:', error)
      return {}
    }
  }
  
  // 获取网络状态
  const getNetworkType = () => {
    uni.getNetworkType({
      success: (res) => {
        networkType.value = res.networkType
        isOnline.value = res.networkType !== 'none'
      },
      fail: (error) => {
        console.error('获取网络状态失败:', error)
      }
    })
  }
  
  // 监听网络状态变化
  const watchNetworkStatus = () => {
    uni.onNetworkStatusChange((res) => {
      networkType.value = res.networkType
      isOnline.value = res.isConnected
    })
  }
  
  // 设置应用版本
  const setAppVersion = (version) => {
    appVersion.value = version
  }
  
  // 更新应用配置
  const updateAppConfig = (config) => {
    appConfig.value = { ...appConfig.value, ...config }
  }
  
  // 初始化应用状态
  const initAppState = () => {
    getSystemInfo()
    getNetworkType()
    watchNetworkStatus()
  }
  
  return {
    // 状态
    appVersion,
    appConfig,
    systemInfo,
    networkType,
    isOnline,
    
    // 计算属性
    appName,
    appDescription,
    
    // 方法
    getSystemInfo,
    getNetworkType,
    watchNetworkStatus,
    setAppVersion,
    updateAppConfig,
    initAppState
  }
}, {
  // 使用 pinia-plugin-unistorage 持久化部分状态
  unistorage: {
    paths: ['appVersion', 'appConfig']
  }
})
