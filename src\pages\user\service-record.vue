<template>
  <view class="service-record-page">
    <!-- 状态栏占位 -->
    <view class="status-bar"></view>
    
    <!-- 页面内容 -->
    <view class="page-content">
      <view class="empty-state">
        <text class="empty-text">服务记录页面</text>
        <text class="empty-desc">功能开发中...</text>
      </view>
    </view>

    <!-- 返回按钮 -->
    <view class="back-button" @click="goBack">
      <text class="back-icon">◀</text>
      <text class="back-text">返回</text>
    </view>
  </view>
</template>

<script setup>
// 返回上一页
const goBack = () => {
  uni.navigateBack({
    delta: 1
  });
};
</script>

<style lang="scss" scoped>
.service-record-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  position: relative;
}

.status-bar {
  height: var(--status-bar-height, 44px);
  background-color: #f5f5f5;
}

.page-content {
  padding: 32rpx;
  
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 400rpx;
    
    .empty-text {
      font-size: 36rpx;
      color: #333;
      margin-bottom: 16rpx;
    }
    
    .empty-desc {
      font-size: 28rpx;
      color: #999;
    }
  }
}

.back-button {
  position: fixed;
  bottom: 80rpx;
  right: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 160rpx;
  height: 80rpx;
  background-color: #52c41a;
  border-radius: 40rpx;
  box-shadow: 0 8rpx 24rpx rgba(82, 196, 26, 0.3);
  
  .back-icon {
    font-size: 28rpx;
    color: #fff;
    margin-right: 8rpx;
  }
  
  .back-text {
    font-size: 28rpx;
    color: #fff;
    font-weight: 500;
  }
}
</style>
