// /**
//  * 字典使用示例
//  * 展示如何在项目中使用字典管理功能
//  */

// import DictManager, { 
//   USER_STATUS, 
//   ORDER_STATUS,
//   GENDER,
// } from './dict.js'

// // ========== 基础使用示例 ==========

// // 1. 根据值获取标签
// console.log('用户状态标签:', DictManager.getLabel(USER_STATUS, 1)) // 输出: 正常



// // 4. 转换为选项数组（用于下拉框）
// const genderOptions = DictManager.toOptions(GENDER)
// console.log('性别选项:', genderOptions)

// // 5. 转换为数组格式
// const statusArray = DictManager.toArray(USER_STATUS)
// console.log('用户状态数组:', statusArray)

// // ========== 实际业务场景示例 ==========

// /**
//  * 格式化用户信息显示
//  * @param {Object} user - 用户对象
//  * @returns {Object} 格式化后的用户信息
//  */
// export function formatUserInfo(user) {
//   return {
//     ...user,
//     genderText: DictManager.getLabel(GENDER, user.gender),
//     statusText: DictManager.getLabel(USER_STATUS, user.status),
//     statusColor: DictManager.getColor(USER_STATUS, user.status),
//     authStatusText: DictManager.getLabel(AUTH_STATUS, user.authStatus),
//     workStatusText: DictManager.getLabel(WORK_STATUS, user.workStatus)
//   }
// }

// /**
//  * 格式化订单信息显示
//  * @param {Object} order - 订单对象
//  * @returns {Object} 格式化后的订单信息
//  */
// export function formatOrderInfo(order) {
//   return {
//     ...order,
//     statusText: DictManager.getLabel(ORDER_STATUS, order.status),
//     statusColor: DictManager.getColor(ORDER_STATUS, order.status),
//     paymentStatusText: DictManager.getLabel(PAYMENT_STATUS, order.paymentStatus),
//     paymentStatusColor: DictManager.getColor(PAYMENT_STATUS, order.paymentStatus),
//     urgencyText: DictManager.getLabel(URGENCY_LEVEL, order.urgency),
//     urgencyColor: DictManager.getColor(URGENCY_LEVEL, order.urgency)
//   }
// }

// /**
//  * 获取登录类型选项（用于表单）
//  * @returns {Array} 登录类型选项数组
//  */
// export function getLoginTypeOptions() {
//   return DictManager.toOptions(LOGIN_TYPE)
// }

// /**
//  * 获取短信验证码类型选项
//  * @returns {Array} 短信验证码类型选项数组
//  */
// export function getSmsCodeTypeOptions() {
//   return DictManager.toOptions(SMS_CODE_TYPE)
// }

// /**
//  * 获取评价等级选项（带图标）
//  * @returns {Array} 评价等级选项数组
//  */
// export function getRatingOptions() {
//   return DictManager.toOptions(RATING_LEVEL).map(item => ({
//     ...item,
//     text: `${item.icon} ${item.label}`
//   }))
// }

// /**
//  * 根据订单状态获取可执行的操作
//  * @param {string} status - 订单状态
//  * @returns {Array} 可执行操作数组
//  */
// export function getOrderActions(status) {
//   const actions = {
//     pending: ['accept', 'reject'],
//     accepted: ['start', 'cancel'],
//     in_progress: ['complete', 'pause'],
//     completed: ['rate', 'complaint'],
//     cancelled: ['delete']
//   }
  
//   return actions[status] || []
// }

// /**
//  * 验证字典值是否有效
//  * @param {Object} dict - 字典对象
//  * @param {*} value - 要验证的值
//  * @returns {boolean} 是否有效
//  */
// export function isValidDictValue(dict, value) {
//   return DictManager.getByValue(dict, value) !== null
// }

// /**
//  * 获取状态统计信息
//  * @param {Array} dataList - 数据列表
//  * @param {string} statusField - 状态字段名
//  * @param {Object} statusDict - 状态字典
//  * @returns {Object} 统计信息
//  */
// export function getStatusStatistics(dataList, statusField, statusDict) {
//   const statistics = {}
  
//   // 初始化统计对象
//   Object.keys(statusDict).forEach(key => {
//     const status = statusDict[key]
//     statistics[status.value] = {
//       label: status.label,
//       color: status.color,
//       count: 0
//     }
//   })
  
//   // 统计各状态数量
//   dataList.forEach(item => {
//     const status = item[statusField]
//     if (statistics[status]) {
//       statistics[status].count++
//     }
//   })
  
//   return statistics
// }

// // ========== Vue 组件中的使用示例 ==========

// /**
//  * 在 Vue 组件中使用字典的示例
//  */
// export const vueComponentExample = {
//   data() {
//     return {
//       // 字典选项
//       genderOptions: DictManager.toOptions(GENDER),
//       statusOptions: DictManager.toOptions(USER_STATUS),
      
//       // 用户数据
//       user: {
//         name: '张三',
//         gender: 1,
//         status: 1
//       }
//     }
//   },
  
//   computed: {
//     // 计算属性：格式化显示
//     userGenderText() {
//       return DictManager.getLabel(GENDER, this.user.gender)
//     },
    
//     userStatusText() {
//       return DictManager.getLabel(USER_STATUS, this.user.status)
//     },
    
//     userStatusColor() {
//       return DictManager.getColor(USER_STATUS, this.user.status)
//     }
//   },
  
//   methods: {
//     // 方法：获取字典标签
//     getDictLabel(dict, value) {
//       return DictManager.getLabel(dict, value)
//     },
    
//     // 方法：获取字典颜色
//     getDictColor(dict, value) {
//       return DictManager.getColor(dict, value)
//     }
//   }
// }

// // ========== 导出常用工具函数 ==========
// export {
//   DictManager,
//   LOGIN_TYPE,
//   SMS_CODE_TYPE,
//   USER_STATUS,
//   ORDER_STATUS,
//   GENDER,
//   PAYMENT_STATUS,
//   PLATFORM_TYPE,
//   AUTH_STATUS,
//   WORK_STATUS,
//   RATING_LEVEL,
//   URGENCY_LEVEL
// }
