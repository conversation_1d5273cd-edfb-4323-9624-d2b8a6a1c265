uni-button,
button {
  border-radius: 44rpx;
  font-weight: 600;
  font-size: 40rpx;
  color: $uni-text-color-inverse;
  background-color: $uni-color-primary;
}
uni-button[size='mini'],
button[size='mini'] {
  font-size: 28rpx;
}

uni-button[type='default'],
button[type='default'] {
  color: $uni-text-color-inverse;
  background-color: $uni-color-primary;
}

uni-button[type='primary'],
button[type='primary'] {
  color: $uni-text-color-inverse;
  background-color: $uni-color-primary;
}

uni-button[type='warn'],
button[type='warn'] {
  color: $uni-text-color-inverse;
  background-color: $uni-color-error;
}

uni-button[disabled],
button[disabled] {
  color: $uni-text-color-inverse;
  background-color: $uni-text-color-disable;
  cursor: not-allowed;
}

uni-button[disabled][type='default'],
uni-button[disabled]:not([type]),
button[disabled][type='default'],
button[disabled]:not([type]) {
  color: $uni-text-color-inverse;
  background-color: $uni-text-color-disable;
}

uni-button[disabled][type='primary'],
button[disabled][type='primary'] {
  background-color: $uni-text-color-disable;
}

uni-button[disabled][type='warn'],
button[disabled][type='warn'] {
  background-color: #ec8b89;
}
uni-button[plain],
button[plain] {
  color: $uni-color-primary;
  //border: 1px solid $uni-color-primary;
  background-color: transparent;
}
uni-button[type='primary'][plain],
button[type='primary'][plain] {
  color: $uni-color-primary;
  //border: 1px solid $uni-color-primary;
  background-color: transparent;
}

uni-button[plain][disabled],
button[plain][disabled] {
  color: $uni-text-color-disable;
  border-color: rgba(0, 0, 0, 0.2);
  background-color: transparent;
}

uni-button[loading][type='primary'] {
  color: rgba(255, 255, 255, 0.6);
  background-color: #0062cc;
}

uni-button[loading][type='primary'][plain] {
  color: $uni-color-primary;
  background-color: transparent;
}

uni-button[loading][type='default'] {
  color: rgba(0, 0, 0, 0.6);
  background-color: #dedede;
}

uni-button[loading][type='default'][plain] {
  color: #353535;
  background-color: transparent;
}

uni-button[loading][type='warn'] {
  color: rgba(255, 255, 255, 0.6);
  background-color: $uni-color-error;
}

uni-button[loading][type='warn'][plain] {
  color: $uni-color-error;
  background-color: transparent;
}

uni-button[loading][native]:before {
  content: none;
}

.button-hover {
  transform: scale(0.95);
}

.button-hover[plain] {
  color: $uni-primary-light;
  border-color: $uni-primary-light;
  background-color: transparent;
}

.button-hover[type='primary'] {
  color: rgba(255, 255, 255, 0.6);
  border-color: $uni-primary-light;
  background-color: $uni-primary-light;
}

.button-hover[type='primary'][plain] {
  color: $uni-primary-light;
  border-color: $uni-primary-light;
  background-color: transparent;
}

.button-hover[type='default'] {
  color: rgba(255, 255, 255, 0.6);
  background-color: $uni-primary-light;
}

.button-hover[type='default'][plain] {
  color: $uni-primary-light;
  border-color: $uni-primary-light;
  background-color: transparent;
}

.button-hover[type='warn'] {
  color: rgba(255, 255, 255, 0.6);
  background-color: $uni-error-light;
}

.button-hover[type='warn'][plain] {
  color: $uni-error-light;
  border-color: $uni-error-light;
  background-color: transparent;
}
